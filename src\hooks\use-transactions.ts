"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import type { Transaction } from "@/db/schema/users"

export function useTransactions(businessId?: string) {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalSales: 0,
    totalExpenses: 0,
    netProfit: 0,
    transactionCount: 0,
  })
  const supabase = createClient()

  useEffect(() => {
    if (businessId) {
      fetchTransactions()
    }
  }, [businessId])

  const fetchTransactions = async () => {
    if (!businessId) return

    try {
      const { data, error } = await supabase
        .from("transactions")
        .select("*")
        .eq("business_id", businessId)
        .order("recorded_at", { ascending: false })

      if (error) throw error

      setTransactions(data || [])
      calculateStats(data || [])
    } catch (error) {
      console.error("Error fetching transactions:", error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (transactionData: Transaction[]) => {
    const sales = transactionData
      .filter((t) => t.type === "sale")
      .reduce((sum, t) => sum + Number.parseFloat(t.amount), 0)

    const expenses = transactionData
      .filter((t) => t.type === "expense")
      .reduce((sum, t) => sum + Number.parseFloat(t.amount), 0)

    setStats({
      totalSales: sales,
      totalExpenses: expenses,
      netProfit: sales - expenses,
      transactionCount: transactionData.length,
    })
  }

  const addTransaction = async (transactionData: {
    type: "sale" | "expense"
    amount: number
    description?: string
  }) => {
    if (!businessId) throw new Error("No business selected")

    try {
      const { data, error } = await supabase
        .from("transactions")
        .insert({
          business_id: businessId,
          type: transactionData.type,
          amount: transactionData.amount.toString(),
          description: transactionData.description,
        })
        .select()
        .single()

      if (error) throw error

      setTransactions((prev) => [data, ...prev])
      calculateStats([data, ...transactions])
      return data
    } catch (error) {
      console.error("Error adding transaction:", error)
      throw error
    }
  }

  const deleteTransaction = async (transactionId: string) => {
    try {
      const { error } = await supabase.from("transactions").delete().eq("id", transactionId)

      if (error) throw error

      setTransactions((prev) => prev.filter((t) => t.id !== transactionId))
      const updatedTransactions = transactions.filter((t) => t.id !== transactionId)
      calculateStats(updatedTransactions)
    } catch (error) {
      console.error("Error deleting transaction:", error)
      throw error
    }
  }

  return {
    transactions,
    stats,
    loading,
    addTransaction,
    deleteTransaction,
    refetch: fetchTransactions,
  }
}
