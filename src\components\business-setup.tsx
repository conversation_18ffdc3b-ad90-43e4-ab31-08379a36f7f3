"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { useBusiness } from "@/hooks/use-business"

const businessTypes = [
  { value: "chigayo", label: "Chigayo (Maize Milling)", subTypes: ["chimanga", "agrifeed"] },
  { value: "retail", label: "Retail Shop", subTypes: ["tech", "hardware", "general"] },
  { value: "service", label: "Service Business", subTypes: ["repair", "consulting", "transport"] },
]

interface BusinessSetupProps {
  onComplete: () => void
}

export function BusinessSetup({ onComplete }: BusinessSetupProps) {
  const [businessName, setBusinessName] = useState("")
  const [businessType, setBusinessType] = useState("")
  const [businessSubType, setBusinessSubType] = useState("")
  const [loading, setLoading] = useState(false)
  const { createBusiness } = useBusiness()

  const selectedType = businessTypes.find((type) => type.value === businessType)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      await createBusiness({
        type: businessType,
        subType: businessSubType,
        name: businessName,
      })
      onComplete()
    } catch (error) {
      console.error("Error creating business:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Set Up Your Business</CardTitle>
        <CardDescription>Tell us about your business to get started</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="businessName">Business Name</Label>
            <Input
              id="businessName"
              value={businessName}
              onChange={(e) => setBusinessName(e.target.value)}
              placeholder="Enter your business name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="businessType">Business Type</Label>
            <Select value={businessType} onValueChange={setBusinessType} required>
              <SelectTrigger>
                <SelectValue placeholder="Select business type" />
              </SelectTrigger>
              <SelectContent>
                {businessTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedType && (
            <div className="space-y-2">
              <Label htmlFor="businessSubType">Business Category</Label>
              <Select value={businessSubType} onValueChange={setBusinessSubType} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {selectedType.subTypes.map((subType) => (
                    <SelectItem key={subType} value={subType}>
                      {subType.charAt(0).toUpperCase() + subType.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <Button
            type="submit"
            disabled={loading || !businessType || !businessSubType}
            className="w-full bg-primary hover:bg-primary/90"
          >
            {loading ? "Setting up..." : "Create Business"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
