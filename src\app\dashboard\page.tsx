"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/src/components/ui/card"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { TransactionForm } from "@/components/transaction-form"
import { TransactionsList } from "@/components/transactions-list"
import { useBusiness } from "@/hooks/use-business"
import { useTransactions } from "@/hooks/use-transactions"
import { useState } from "react"
import { ShoppingCart, DollarSign, TrendingUp, Package, Settings, BarChart3, Plus } from "lucide-react"

export default function DashboardPage() {
  const { currentBusiness } = useBusiness()
  const { stats, loading } = useTransactions(currentBusiness?.id)
  const [showTransactionForm, setShowTransactionForm] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-MW", {
      style: "currency",
      currency: "MWK",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const dashboardStats = [
    {
      title: "Total Sales",
      value: loading ? "Loading..." : formatCurrency(stats.totalSales),
      change: "+12%",
      icon: DollarSign,
    },
    {
      title: "Total Expenses",
      value: loading ? "Loading..." : formatCurrency(stats.totalExpenses),
      change: "+5%",
      icon: TrendingUp,
    },
    {
      title: "Net Profit",
      value: loading ? "Loading..." : formatCurrency(stats.netProfit),
      change: "+18%",
      icon: Package,
    },
    {
      title: "Transactions",
      value: loading ? "Loading..." : stats.transactionCount.toString(),
      change: "+8%",
      icon: ShoppingCart,
    },
  ]

  const quickActions = [
    {
      title: "New Sale",
      description: "Record a new sale transaction",
      icon: ShoppingCart,
      action: () => setShowTransactionForm(true),
    },
    {
      title: "Add Expense",
      description: "Record a business expense",
      icon: Package,
      action: () => setShowTransactionForm(true),
    },
    {
      title: "View Reports",
      description: "Check business analytics",
      icon: BarChart3,
      action: () => {},
    },
    {
      title: "Settings",
      description: "Manage business settings",
      icon: Settings,
      action: () => {},
    },
  ]

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-text-light dark:text-text-dark">Welcome back!</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Here's what's happening with {currentBusiness?.name} today.
          </p>
        </div>
        <Button onClick={() => setShowTransactionForm(true)} className="bg-primary hover:bg-primary/90">
          <Plus className="h-4 w-4 mr-2" />
          New Transaction
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat) => (
          <Card key={stat.title} className="bg-card-light dark:bg-card-dark">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-text-light dark:text-text-dark">{stat.value}</div>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                {stat.change} from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Transaction Form Modal */}
      {showTransactionForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowTransactionForm(false)}
              className="absolute -top-2 -right-2 z-10 bg-white dark:bg-gray-800 rounded-full"
            >
              ✕
            </Button>
            <TransactionForm onSuccess={() => setShowTransactionForm(false)} />
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div>
        <h3 className="text-xl font-semibold text-text-light dark:text-text-dark mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action) => (
            <Card
              key={action.title}
              className="bg-card-light dark:bg-card-dark hover:shadow-lg transition-shadow cursor-pointer"
            >
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <action.icon className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">{action.title}</CardTitle>
                </div>
                <CardDescription>{action.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={action.action} className="w-full bg-primary hover:bg-primary/90">
                  Get Started
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Transactions */}
      <TransactionsList />
    </div>
  )
}
