"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/src/components/ui/button"
import { Input } from "@/src/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card"
import { Label } from "@/src/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select"
import { Textarea } from "@/src/components/ui/textarea"
import { Alert, AlertDescription } from "@/src/components/ui/alert"
import { useTransactions } from "@/hooks/use-transactions"
import { useBusiness } from "@/hooks/use-business"
import { DollarSign, Plus } from "lucide-react"

interface TransactionFormProps {
  onSuccess?: () => void
}

export function TransactionForm({ onSuccess }: TransactionFormProps) {
  const [type, setType] = useState<"sale" | "expense">("sale")
  const [amount, setAmount] = useState("")
  const [description, setDescription] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const { currentBusiness } = useBusiness()
  const { addTransaction } = useTransactions(currentBusiness?.id)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!currentBusiness) return

    setLoading(true)
    setError("")

    try {
      const amountNum = Number.parseFloat(amount)
      if (isNaN(amountNum) || amountNum <= 0) {
        throw new Error("Please enter a valid amount")
      }

      await addTransaction({
        type,
        amount: amountNum,
        description: description.trim() || undefined,
      })

      // Reset form
      setAmount("")
      setDescription("")
      onSuccess?.()
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto bg-card-light dark:bg-card-dark">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Plus className="h-5 w-5 text-primary" />
          <span>Record Transaction</span>
        </CardTitle>
        <CardDescription>Add a new sale or expense to your business</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="type">Transaction Type</Label>
            <Select value={type} onValueChange={(value: "sale" | "expense") => setType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sale">💰 Sale (Income)</SelectItem>
                <SelectItem value="expense">💸 Expense</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount (MWK)</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className="pl-10"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="What was this transaction for?"
              rows={3}
            />
          </div>

          <Button type="submit" disabled={loading || !amount} className="w-full bg-primary hover:bg-primary/90">
            {loading ? "Recording..." : `Record ${type === "sale" ? "Sale" : "Expense"}`}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
