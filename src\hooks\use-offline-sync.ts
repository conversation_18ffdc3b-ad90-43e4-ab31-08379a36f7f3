"use client"

import { useState, useEffect } from "react"

export function useOfflineSync() {
  const [isOnline, setIsOnline] = useState(true)
  const [pendingSync, setPendingSync] = useState(0)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    // Check initial status
    setIsOnline(navigator.onLine)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  const addToSyncQueue = (data: any) => {
    // In a real app, this would store to IndexedDB
    setPendingSync((prev) => prev + 1)
    console.log("Added to sync queue:", data)
  }

  const syncPendingData = async () => {
    // In a real app, this would sync with Supabase
    console.log("Syncing pending data...")
    setPendingSync(0)
  }

  return {
    isOnline,
    pendingSync,
    addToSyncQueue,
    syncPendingData,
  }
}
