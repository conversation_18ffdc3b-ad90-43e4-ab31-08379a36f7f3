"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { TransactionForm } from "@/components/transaction-form"
import { TransactionsList } from "@/components/transactions-list"
import { useTransactions } from "@/hooks/use-transactions"
import { useBusiness } from "@/hooks/use-business"
import { Plus, TrendingUp, TrendingDown, DollarSign, Receipt } from "lucide-react"

export default function TransactionsPage() {
  const [showForm, setShowForm] = useState(false)
  const { currentBusiness } = useBusiness()
  const { stats, loading } = useTransactions(currentBusiness?.id)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-MW", {
      style: "currency",
      currency: "MWK",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="space-y-8">
      {/* <PERSON><PERSON> */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-text-light dark:text-text-dark">Transactions</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Manage your business income and expenses</p>
        </div>
        <Button onClick={() => setShowForm(true)} className="bg-primary hover:bg-primary/90">
          <Plus className="h-4 w-4 mr-2" />
          New Transaction
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-card-light dark:bg-card-dark">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Sales</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {loading ? "Loading..." : formatCurrency(stats.totalSales)}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card-light dark:bg-card-dark">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Expenses</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {loading ? "Loading..." : formatCurrency(stats.totalExpenses)}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card-light dark:bg-card-dark">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Net Profit</CardTitle>
            <DollarSign className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${stats.netProfit >= 0 ? "text-green-600" : "text-red-600"}`}>
              {loading ? "Loading..." : formatCurrency(stats.netProfit)}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card-light dark:bg-card-dark">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Transactions</CardTitle>
            <Receipt className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-text-light dark:text-text-dark">
              {loading ? "Loading..." : stats.transactionCount}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transaction Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowForm(false)}
              className="absolute -top-2 -right-2 z-10 bg-white dark:bg-gray-800 rounded-full"
            >
              ✕
            </Button>
            <TransactionForm onSuccess={() => setShowForm(false)} />
          </div>
        </div>
      )}

      {/* Transactions List */}
      <TransactionsList />
    </div>
  )
}
