import { pgTable, uuid, text, timestamp, decimal } from "drizzle-orm/pg-core"

export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: text("email").notNull().unique(),
  fullName: text("full_name").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
})

export const businesses = pgTable("businesses", {
  id: uuid("id").primaryKey().defaultRandom(),
  ownerId: uuid("owner_id")
    .notNull()
    .references(() => users.id),
  type: text("type").notNull(), // e.g., 'chigayo', 'retail'
  subType: text("sub_type").notNull(), // e.g., 'chimanga', 'agrifeed'
  name: text("name"),
  createdAt: timestamp("created_at").defaultNow(),
})

export const transactions = pgTable("transactions", {
  id: uuid("id").primaryKey().defaultRandom(),
  businessId: uuid("business_id")
    .notNull()
    .references(() => businesses.id),
  type: text("type").notNull(), // 'sale', 'expense'
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  description: text("description"),
  recordedAt: timestamp("recorded_at").defaultNow(),
})

export type User = typeof users.$inferSelect
export type NewUser = typeof users.$inferInsert
export type Business = typeof businesses.$inferSelect
export type NewBusiness = typeof businesses.$inferInsert
export type Transaction = typeof transactions.$inferSelect
export type NewTransaction = typeof transactions.$inferInsert
