"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import type { Business } from "@/db/schema/users"

export function useBusiness() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [currentBusiness, setCurrentBusiness] = useState<Business | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    fetchBusinesses()
  }, [])

  const fetchBusinesses = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (!user) return

      const { data, error } = await supabase.from("businesses").select("*").eq("owner_id", user.id)

      if (error) throw error
      setBusinesses(data || [])
      if (data && data.length > 0) {
        setCurrentBusiness(data[0])
      }
    } catch (error) {
      console.error("Error fetching businesses:", error)
    } finally {
      setLoading(false)
    }
  }

  const createBusiness = async (businessData: {
    type: string
    subType: string
    name?: string
  }) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser()
      if (!user) throw new Error("No user found")

      const { data, error } = await supabase
        .from("businesses")
        .insert({
          owner_id: user.id,
          type: businessData.type,
          sub_type: businessData.subType,
          name: businessData.name,
        })
        .select()
        .single()

      if (error) throw error

      setBusinesses((prev) => [...prev, data])
      setCurrentBusiness(data)
      return data
    } catch (error) {
      console.error("Error creating business:", error)
      throw error
    }
  }

  return {
    businesses,
    currentBusiness,
    setCurrentBusiness,
    createBusiness,
    loading,
    refetch: fetchBusinesses,
  }
}
