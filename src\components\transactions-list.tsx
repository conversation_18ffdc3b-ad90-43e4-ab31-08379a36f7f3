"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/src/components/ui/card"
import { Button } from "@/src/components/ui/button"
import { useTransactions } from "@/hooks/use-transactions"
import { useBusiness } from "@/hooks/use-business"
import { formatDistanceToNow } from "date-fns"
import { Trash2, TrendingUp, TrendingDown, Receipt } from "lucide-react"

export function TransactionsList() {
  const { currentBusiness } = useBusiness()
  const { transactions, loading, deleteTransaction } = useTransactions(currentBusiness?.id)

  if (loading) {
    return (
      <Card className="bg-card-light dark:bg-card-dark">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (transactions.length === 0) {
    return (
      <Card className="bg-card-light dark:bg-card-dark">
        <CardContent className="p-6 text-center">
          <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">No transactions recorded yet</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">Start by recording your first sale or expense</p>
        </CardContent>
      </Card>
    )
  }

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("en-MW", {
      style: "currency",
      currency: "MWK",
      minimumFractionDigits: 0,
    }).format(Number.parseFloat(amount))
  }

  return (
    <Card className="bg-card-light dark:bg-card-dark">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Receipt className="h-5 w-5 text-primary" />
          <span>Recent Transactions</span>
        </CardTitle>
        <CardDescription>Your latest business transactions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div
                  className={`p-2 rounded-full ${
                    transaction.type === "sale" ? "bg-green-100 dark:bg-green-900" : "bg-red-100 dark:bg-red-900"
                  }`}
                >
                  {transaction.type === "sale" ? (
                    <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600 dark:text-red-400" />
                  )}
                </div>
                <div>
                  <p className="font-medium text-text-light dark:text-text-dark">
                    {transaction.description || `${transaction.type === "sale" ? "Sale" : "Expense"}`}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDistanceToNow(new Date(transaction.recordedAt!), { addSuffix: true })}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <span
                  className={`font-semibold ${
                    transaction.type === "sale"
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400"
                  }`}
                >
                  {transaction.type === "sale" ? "+" : "-"}
                  {formatCurrency(transaction.amount)}
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => deleteTransaction(transaction.id)}
                  className="h-8 w-8 text-gray-400 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
