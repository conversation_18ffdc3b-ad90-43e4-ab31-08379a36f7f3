export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          created_at: string | null
        }
        Insert: {
          id?: string
          email: string
          full_name: string
          created_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          created_at?: string | null
        }
      }
      businesses: {
        Row: {
          id: string
          owner_id: string
          type: string
          sub_type: string
          name: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          owner_id: string
          type: string
          sub_type: string
          name?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          owner_id?: string
          type?: string
          sub_type?: string
          name?: string | null
          created_at?: string | null
        }
      }
      transactions: {
        Row: {
          id: string
          business_id: string
          type: string
          amount: string
          description: string | null
          recorded_at: string | null
        }
        Insert: {
          id?: string
          business_id: string
          type: string
          amount: string
          description?: string | null
          recorded_at?: string | null
        }
        Update: {
          id?: string
          business_id?: string
          type?: string
          amount?: string
          description?: string | null
          recorded_at?: string | null
        }
      }
    }
  }
}
